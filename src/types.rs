use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::BTreeMap;

use crate::account::types::TradeRecord;
use crate::config::DataSourceType;

/// 交易所类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Exchange {
    Binance,
    Okx,
    Bybit,
}

/// 客户端数据格式类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ClientFormat {
    /// Binance格式
    Binance,
    /// OKX格式
    Okx,
}

/// 订单类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum OrderType {
    Market,
    Limit,
    /// IOC (Immediate or Cancel) 限价单
    /// 立即成交，不能成交的部分立即取消
    LimitIOC,
    /// GTX (Good Till Crossing) 限价单
    /// 只能作为maker成交，如果会成为taker则取消订单
    LimitGTX,
}

/// 订单方向
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum OrderSide {
    Buy,
    Sell,
}

/// 订单状态
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum OrderStatus {
    Pending,
    PartiallyFilled,
    Filled,
    Cancelled,
    /// IOC订单过期（无法立即成交）
    Expired,
    /// 订单被拒绝（资金不足等）
    Rejected,
}

/// 价格类型 - 使用有序的包装器
#[derive(Debug, Clone, Copy, PartialEq, PartialOrd, Serialize, Deserialize)]
pub struct Price(pub f64);

impl Price {
    pub fn new(value: f64) -> Self {
        Self(value)
    }

    pub fn value(&self) -> f64 {
        self.0
    }
}

impl Eq for Price {}

impl Ord for Price {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        self.0
            .partial_cmp(&other.0)
            .unwrap_or(std::cmp::Ordering::Equal)
    }
}

impl From<f64> for Price {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

impl From<Price> for f64 {
    fn from(price: Price) -> Self {
        price.0
    }
}

impl std::fmt::Display for Price {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl std::ops::Add for Price {
    type Output = Self;

    fn add(self, other: Self) -> Self {
        Self(self.0 + other.0)
    }
}

impl std::ops::Sub for Price {
    type Output = Self;

    fn sub(self, other: Self) -> Self {
        Self(self.0 - other.0)
    }
}

impl std::ops::Mul<f64> for Price {
    type Output = Self;

    fn mul(self, other: f64) -> Self {
        Self(self.0 * other)
    }
}

impl std::ops::Div<f64> for Price {
    type Output = Self;

    fn div(self, other: f64) -> Self {
        Self(self.0 / other)
    }
}

/// 数量和更新ID类型
pub type Quantity = f64;
pub type UpdateId = u64;

/// 订单簿快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderBookSnapshot {
    #[serde(rename = "E")]
    pub timestamp: u64,
    #[serde(rename = "lastUpdateId")]
    pub update_id: u64,
    pub bids: BTreeMap<Price, Quantity>,
    pub asks: BTreeMap<Price, Quantity>,
}

/// BBO (Best Bid Offer)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Bbo {
    pub update_id: UpdateId,
    pub bid_price: Price,
    pub bid_quantity: Quantity,
    pub ask_price: Price,
    pub ask_quantity: Quantity,
    /// 时间戳（微秒级，与 TradeData 保持一致）
    pub timestamp: Option<u64>,
    pub data_source_type: DataSourceType,
}

impl Bbo {
    /// 转换为DateTime格式的时间戳
    pub fn timestamp_datetime(&self) -> Option<DateTime<Utc>> {
        self.timestamp
            .and_then(|ts| DateTime::from_timestamp_micros(ts as i64))
    }

    /// 获取时间戳用于排序（如果没有时间戳则返回0）
    pub fn timestamp_for_sorting(&self) -> u64 {
        self.timestamp.unwrap_or(0)
    }
}

/// BookTicker数据 - 来自CSV文件的实时最优买卖价格数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BookTicker {
    /// 更新ID
    pub update_id: UpdateId,
    /// 最优买价
    pub best_bid_price: Price,
    /// 最优买量
    pub best_bid_qty: Quantity,
    /// 最优卖价
    pub best_ask_price: Price,
    /// 最优卖量
    pub best_ask_qty: Quantity,
    /// 交易时间戳（毫秒）
    pub transaction_time: u64,
    /// 事件时间戳（毫秒）
    pub event_time: u64,
}

impl BookTicker {
    /// 创建新的BookTicker实例
    pub fn new(
        update_id: UpdateId,
        best_bid_price: Price,
        best_bid_qty: Quantity,
        best_ask_price: Price,
        best_ask_qty: Quantity,
        transaction_time: u64,
        event_time: u64,
    ) -> Self {
        Self {
            update_id,
            best_bid_price,
            best_bid_qty,
            best_ask_price,
            best_ask_qty,
            transaction_time,
            event_time,
        }
    }

    /// 获取买卖价差
    pub fn spread(&self) -> Price {
        self.best_ask_price - self.best_bid_price
    }

    /// 获取中间价
    pub fn mid_price(&self) -> Price {
        (self.best_bid_price + self.best_ask_price) / 2.0
    }

    /// 转换为DateTime格式的交易时间
    pub fn transaction_datetime(&self) -> DateTime<Utc> {
        DateTime::from_timestamp_millis(self.transaction_time as i64).unwrap_or_else(|| Utc::now())
    }

    /// 转换为DateTime格式的事件时间
    pub fn event_datetime(&self) -> DateTime<Utc> {
        DateTime::from_timestamp_millis(self.event_time as i64).unwrap_or_else(|| Utc::now())
    }

    /// 转换为BBO格式（兼容性）
    pub fn to_bbo(&self) -> Bbo {
        Bbo {
            update_id: self.update_id,
            bid_price: self.best_bid_price,
            bid_quantity: self.best_bid_qty,
            ask_price: self.best_ask_price,
            ask_quantity: self.best_ask_qty,
            // 使用 transaction_time 转换为微秒级时间戳
            timestamp: Some(self.transaction_time * 1000),
            data_source_type: DataSourceType::BinanceOfficial,
        }
    }
}

/// 交易数据 - 成交数据（来自CSV文件）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeData {
    /// 交易所名称
    pub exchange: String,
    /// 交易对
    pub symbol: String,
    /// 交易时间戳（微秒）
    pub timestamp: u64,
    /// 本地时间戳（微秒）
    pub local_timestamp: u64,
    /// 交易ID
    pub id: String,
    /// 交易方向
    pub side: OrderSide,
    /// 成交价格
    pub price: Price,
    /// 成交数量
    pub amount: f64,
    pub data_source_type: DataSourceType,
}

impl TradeData {
    /// 转换为DateTime格式的时间戳
    pub fn timestamp_datetime(&self) -> DateTime<Utc> {
        DateTime::from_timestamp_micros(self.timestamp as i64).unwrap_or_else(|| Utc::now())
    }

    /// 转换为Trade格式（兼容性）
    pub fn to_trade(&self) -> Trade {
        Trade {
            id: self.id.clone(),
            symbol: self.symbol.clone(),
            price: self.price,
            quantity: self.amount,
            side: self.side,
            timestamp: Some(self.timestamp_datetime()),
            data_source_type: self.data_source_type,
        }
    }
}

/// 交易记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Trade {
    pub id: String,
    pub symbol: String,
    pub price: Price,
    pub quantity: Quantity,
    pub side: OrderSide,
    pub timestamp: Option<DateTime<Utc>>,
    pub data_source_type: DataSourceType,
}

/// 订单
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Order {
    pub id: String,
    pub client_order_id: String,
    pub symbol: String,
    pub order_type: OrderType,
    pub side: OrderSide,
    pub price: Option<Price>,
    pub quantity: Quantity,
    pub status: OrderStatus,
    pub timestamp: DateTime<Utc>,
    /// 执行信息（用于订单更新推送）
    pub execution_info: Option<OrderExecutionInfo>,
}

/// 订单执行信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderExecutionInfo {
    /// 最后成交价格
    pub last_filled_price: Option<Price>,
    /// 最后成交数量
    pub last_filled_quantity: f64,
    /// 累计成交数量
    pub filled_quantity: f64,
    /// 平均成交价格
    pub average_price: Option<Price>,
    /// 手续费
    pub commission: f64,
    /// 手续费资产
    pub commission_asset: String,
    /// 交易ID
    pub trade_id: Option<String>,
}

/// 取消订单请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CancelOrderRequest {
    /// 订单ID（系统内部ID）
    pub order_id: Option<String>,
    /// 客户端订单ID
    pub client_order_id: Option<String>,
    /// 交易对符号
    pub symbol: String,
    /// 请求时间戳
    pub timestamp: DateTime<Utc>,
}

/// 取消订单结果
#[derive(Debug, Clone)]
pub enum CancelOrderResult {
    /// 取消成功，返回被取消的订单
    Success(Order),
    /// 订单未找到
    NotFound,
    /// 取消失败，返回错误信息
    Failed(String),
}

/// 带响应通道的取消订单请求（用于HTTP API）
#[derive(Debug)]
pub struct CancelOrderRequestWithResponse {
    /// 基本的取消订单请求
    pub request: CancelOrderRequest,
    /// 响应通道，用于返回取消结果
    pub response_tx: tokio::sync::oneshot::Sender<CancelOrderResult>,
}

/// 市场数据类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MarketData {
    OrderBook(OrderBookSnapshot),
    Bbo(Bbo),
    Trade(Trade),
    BookTicker(BookTicker),
    TradeData(TradeData),
}

impl MarketData {
    pub fn data_source_type(&self) -> DataSourceType {
        match self {
            MarketData::OrderBook(_) => DataSourceType::BinanceTardis,
            MarketData::Bbo(bbo) => bbo.data_source_type,
            MarketData::Trade(trade) => trade.data_source_type,
            MarketData::BookTicker(_) => DataSourceType::BinanceOfficial,
            MarketData::TradeData(trade_data) => trade_data.data_source_type,
        }
    }

    /// 获取市场数据的时间戳（微秒级），用于时间同步
    pub fn timestamp_for_sorting(&self) -> u64 {
        match self {
            MarketData::OrderBook(snapshot) => {
                // OrderBookSnapshot的timestamp已经是u64类型
                snapshot.timestamp
            }
            MarketData::Bbo(bbo) => bbo.timestamp_for_sorting(),
            MarketData::Trade(trade) => trade
                .timestamp
                .map(|ts| ts.timestamp_micros() as u64)
                .unwrap_or(0),
            MarketData::BookTicker(bookticker) => {
                // 使用 transaction_time 转换为微秒级
                bookticker.transaction_time * 1000
            }
            MarketData::TradeData(trade_data) => trade_data.timestamp,
        }
    }

    /// 获取市场数据的 DateTime 时间戳
    pub fn timestamp_datetime(&self) -> Option<DateTime<Utc>> {
        match self {
            MarketData::OrderBook(snapshot) => {
                // 将u64时间戳转换为DateTime
                DateTime::from_timestamp_micros(snapshot.timestamp as i64)
            }
            MarketData::Bbo(bbo) => bbo.timestamp_datetime(),
            MarketData::Trade(trade) => trade.timestamp,
            MarketData::BookTicker(bookticker) => Some(bookticker.transaction_datetime()),
            MarketData::TradeData(trade_data) => Some(trade_data.timestamp_datetime()),
        }
    }
}

/// WebSocket订阅类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum SubscriptionType {
    OrderBook,
    Bbo,
    Trade,
    Indicators,
    BookTicker,
    OrderAndFill,
}

/// 时间屏障类型
#[derive(Debug, Clone)]
pub enum TimeBarrier {
    Timestamp(DateTime<Utc>),
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json;

    #[test]
    fn test_bookticker_creation() {
        let bookticker = BookTicker {
            update_id: 12345,
            best_bid_price: Price::new(99.5),
            best_bid_qty: 10.0,
            best_ask_price: Price::new(100.5),
            best_ask_qty: 15.0,
            transaction_time: 1640995200000,
            event_time: 1640995200000,
        };

        assert_eq!(bookticker.update_id, 12345);
        assert_eq!(bookticker.best_bid_price.value(), 99.5);
        assert_eq!(bookticker.best_ask_price.value(), 100.5);
        assert_eq!(bookticker.best_bid_qty, 10.0);
        assert_eq!(bookticker.best_ask_qty, 15.0);
    }

    #[test]
    fn test_bookticker_serialization() {
        let bookticker = BookTicker {
            update_id: 12345,
            best_bid_price: Price::new(99.5),
            best_bid_qty: 10.0,
            best_ask_price: Price::new(100.5),
            best_ask_qty: 15.0,
            transaction_time: 1640995200000,
            event_time: 1640995200000,
        };

        // 测试序列化
        let json_str = serde_json::to_string(&bookticker).expect("Should serialize");
        let parsed: serde_json::Value = serde_json::from_str(&json_str).expect("Should parse");

        assert_eq!(parsed["update_id"], 12345);
        assert_eq!(parsed["best_bid_price"], 99.5);
        assert_eq!(parsed["best_ask_price"], 100.5);
        assert_eq!(parsed["best_bid_qty"], 10.0);
        assert_eq!(parsed["best_ask_qty"], 15.0);

        // 测试反序列化
        let deserialized: BookTicker = serde_json::from_str(&json_str).expect("Should deserialize");
        assert_eq!(deserialized.update_id, bookticker.update_id);
        assert_eq!(deserialized.best_bid_price, bookticker.best_bid_price);
        assert_eq!(deserialized.best_ask_price, bookticker.best_ask_price);
    }

    #[test]
    fn test_bookticker_mid_price() {
        let bookticker = BookTicker {
            update_id: 12345,
            best_bid_price: Price::new(99.5),
            best_bid_qty: 10.0,
            best_ask_price: Price::new(100.5),
            best_ask_qty: 15.0,
            transaction_time: 1640995200000,
            event_time: 1640995200000,
        };

        let mid_price = bookticker.mid_price();
        assert_eq!(mid_price.value(), 100.0); // (99.5 + 100.5) / 2
    }

    #[test]
    fn test_bookticker_spread() {
        let bookticker = BookTicker {
            update_id: 12345,
            best_bid_price: Price::new(99.5),
            best_bid_qty: 10.0,
            best_ask_price: Price::new(100.5),
            best_ask_qty: 15.0,
            transaction_time: 1640995200000,
            event_time: 1640995200000,
        };

        let spread = bookticker.spread();
        assert_eq!(spread.value(), 1.0); // 100.5 - 99.5
    }

    #[test]
    fn test_bookticker_to_bbo() {
        let bookticker = BookTicker {
            update_id: 12345,
            best_bid_price: Price::new(99.5),
            best_bid_qty: 10.0,
            best_ask_price: Price::new(100.5),
            best_ask_qty: 15.0,
            transaction_time: 1640995200000,
            event_time: 1640995200000,
        };

        let bbo = bookticker.to_bbo();
        assert_eq!(bbo.update_id, bookticker.update_id);
        assert_eq!(bbo.bid_price, bookticker.best_bid_price);
        assert_eq!(bbo.ask_price, bookticker.best_ask_price);
        assert_eq!(bbo.bid_quantity, bookticker.best_bid_qty);
        assert_eq!(bbo.ask_quantity, bookticker.best_ask_qty);
        assert_eq!(bbo.timestamp, Some(bookticker.transaction_time * 1000));
    }
}

/// 回测总结数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BacktestSummary {
    /// 回测开始时间
    pub start_time: DateTime<Utc>,
    /// 回测结束时间
    pub end_time: DateTime<Utc>,
    /// 总盈亏（USDT）
    pub total_pnl: f64,
    /// 下单次数
    pub total_orders: usize,
    /// 成交次数
    pub total_fills: usize,
    /// 胜率（百分比）
    pub win_rate: f64,
    /// 年化收益率（百分比）
    pub annual_return: f64,
    /// 最大回撤（百分比）
    pub max_drawdown: f64,
    /// 交易记录
    pub trades: Vec<BacktestTrade>,
    /// 订单记录
    pub orders: Vec<BacktestOrder>,
    /// BBO记录（用于K线图）
    pub bbo_records: Vec<BacktestBbo>,
}

/// 回测交易记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BacktestTrade {
    /// 交易ID
    pub trade_id: String,
    /// 订单ID
    pub order_id: String,
    /// 交易对
    pub symbol: String,
    /// 交易方向
    pub side: OrderSide,
    /// 成交价格
    pub price: Price,
    /// 成交数量
    pub quantity: f64,
    /// 手续费
    pub fee: f64,
    /// 手续费币种
    pub fee_asset: String,
    /// 交易时间
    pub timestamp: DateTime<Utc>,
    /// 是否为maker
    pub is_maker: bool,
    /// 盈亏（相对于开仓价格）
    pub pnl: f64,
}

/// 回测订单记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BacktestOrder {
    /// 订单ID
    pub order_id: String,
    /// 客户端订单ID
    pub client_order_id: String,
    /// 交易对
    pub symbol: String,
    /// 订单类型
    pub order_type: OrderType,
    /// 订单方向
    pub side: OrderSide,
    /// 订单价格
    pub price: Option<Price>,
    /// 订单数量（原始下单数量，不会被修改）
    pub quantity: f64,
    /// 订单状态
    pub status: OrderStatus,
    /// 下单时间
    pub timestamp: DateTime<Utc>,
    /// 成交时间（如果已成交）
    pub filled_timestamp: Option<DateTime<Utc>>,
    /// 成交价格
    pub filled_price: Option<Price>,
    /// 成交数量
    pub filled_quantity: f64,
    /// 手续费
    pub fee: f64,
    /// 手续费币种
    pub fee_asset: String,
}

/// 回测BBO记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BacktestBbo {
    /// 时间戳
    pub timestamp: DateTime<Utc>,
    /// 买价
    pub bid_price: Price,
    /// 买量
    pub bid_quantity: f64,
    /// 卖价
    pub ask_price: Price,
    /// 卖量
    pub ask_quantity: f64,
}

/// 回测数据记录器
#[derive(Debug, Clone)]
pub struct BacktestRecorder {
    /// 是否正在记录
    pub is_recording: bool,
    /// 回测开始时间
    pub start_time: Option<DateTime<Utc>>,
    /// 回测结束时间
    pub end_time: Option<DateTime<Utc>>,
    /// BBO记录
    pub bbo_records: Vec<BacktestBbo>,
    /// 订单记录
    pub orders: Vec<BacktestOrder>,
    /// 交易记录
    pub trades: Vec<BacktestTrade>,
}

impl BacktestRecorder {
    /// 创建新的回测记录器
    pub fn new() -> Self {
        Self {
            is_recording: false,
            start_time: None,
            end_time: None,
            bbo_records: Vec::new(),
            orders: Vec::new(),
            trades: Vec::new(),
        }
    }

    /// 开始记录
    pub fn start_recording(&mut self, start_time: DateTime<Utc>) {
        tracing::info!("start_recording: {:?}", start_time);
        self.is_recording = true;
        self.start_time = Some(start_time);
        self.bbo_records.clear();
        self.orders.clear();
        self.trades.clear();
    }

    /// 停止记录
    pub fn stop_recording(&mut self, end_time: DateTime<Utc>) {
        tracing::info!("stop_recording: {:?}", end_time);
        self.is_recording = false;
        self.end_time = Some(end_time);
    }

    /// 记录BBO数据
    pub fn record_bbo(&mut self, bbo: &Bbo) {
        if !self.is_recording {
            return;
        }

        if let Some(timestamp) = bbo.timestamp_datetime() {
            self.bbo_records.push(BacktestBbo {
                timestamp,
                bid_price: bbo.bid_price,
                bid_quantity: bbo.bid_quantity,
                ask_price: bbo.ask_price,
                ask_quantity: bbo.ask_quantity,
            });
        }
    }

    /// 记录订单
    pub fn record_order(&mut self, order: &Order) {
        if !self.is_recording {
            return;
        }

        // 检查是否已经存在相同的订单，如果存在则更新成交信息
        if let Some(existing_order) = self.orders.iter_mut().find(|o| o.order_id == order.id) {
            // 更新订单状态和成交信息
            existing_order.status = order.status.clone();

            // 如果有执行信息，更新成交信息
            if let Some(exec_info) = &order.execution_info {
                existing_order.filled_quantity = exec_info.filled_quantity;
                existing_order.filled_price = exec_info.average_price;
                existing_order.fee = exec_info.commission;
                // 如果订单已成交，设置成交时间为当前订单时间戳
                if order.status == OrderStatus::Filled
                    || order.status == OrderStatus::PartiallyFilled
                {
                    existing_order.filled_timestamp = Some(order.timestamp);
                }
            }

            return;
        }

        let backtest_order = BacktestOrder {
            order_id: order.id.clone(),
            client_order_id: order.client_order_id.clone(),
            symbol: order.symbol.clone(),
            order_type: order.order_type.clone(),
            side: order.side,
            price: order.price,
            quantity: order.quantity,
            status: order.status.clone(),
            timestamp: order.timestamp, // 这里使用订单的时间戳，应该已经在matching engine中被更新为市场数据时间戳
            filled_timestamp: if order.status == OrderStatus::Filled
                || order.status == OrderStatus::PartiallyFilled
            {
                Some(order.timestamp) // 如果已成交，成交时间与订单时间戳相同（因为都是市场数据时间戳）
            } else {
                None
            },
            filled_price: order
                .execution_info
                .as_ref()
                .and_then(|ei| ei.average_price),
            filled_quantity: order
                .execution_info
                .as_ref()
                .map(|ei| ei.filled_quantity)
                .unwrap_or(0.0),
            fee: order
                .execution_info
                .as_ref()
                .map(|ei| ei.commission)
                .unwrap_or(0.0),
            fee_asset: "USDT".to_string(),
        };

        self.orders.push(backtest_order);
    }

    /// 记录交易
    pub fn record_trade(&mut self, trade: &TradeRecord) {
        if !self.is_recording {
            return;
        }

        let backtest_trade = BacktestTrade {
            trade_id: trade.trade_id.clone(),
            order_id: trade.order_id.clone(),
            symbol: trade.symbol.clone(),
            side: trade.side,
            price: trade.price,
            quantity: trade.quantity,
            fee: trade.fee,
            fee_asset: trade.fee_asset.clone(),
            timestamp: trade.timestamp,
            is_maker: trade.is_maker,
            pnl: 0.0, // 需要后续计算
        };

        self.trades.push(backtest_trade);
    }

    /// 生成回测总结
    pub fn generate_summary(&self) -> Option<BacktestSummary> {
        if self.start_time.is_none() {
            tracing::info!("start_time is none");
            return None;
        }

        let start_time = self.start_time.unwrap();
        let end_time = self.end_time.unwrap_or_else(|| chrono::Utc::now());

        // 计算总盈亏
        let total_pnl = self.trades.iter().map(|t| t.pnl).sum();

        // 计算成交次数
        let total_fills = self.trades.len();

        // 计算胜率
        let winning_trades = self.trades.iter().filter(|t| t.pnl > 0.0).count();
        let win_rate = if total_fills > 0 {
            (winning_trades as f64 / total_fills as f64) * 100.0
        } else {
            0.0
        };

        // 计算年化收益率
        let duration = end_time - start_time;
        let days = duration.num_seconds() as f64 / 86400.0;
        let annual_return = if days > 0.0 {
            (total_pnl / 10000.0) * (365.0 / days) * 100.0 // 假设初始资金10000
        } else {
            0.0
        };

        // 计算最大回撤（简化版本）
        let mut max_drawdown = 0.0;
        let mut peak = 10000.0; // 初始资金
        let mut current = 10000.0;

        for trade in &self.trades {
            current += trade.pnl;
            if current > peak {
                peak = current;
            }
            let drawdown = (peak - current) / peak * 100.0;
            if drawdown > max_drawdown {
                max_drawdown = drawdown;
            }
        }

        Some(BacktestSummary {
            start_time,
            end_time,
            total_pnl,
            total_orders: self.orders.len(),
            total_fills,
            win_rate,
            annual_return,
            max_drawdown,
            trades: self.trades.clone(),
            orders: self.orders.clone(),
            bbo_records: self.bbo_records.clone(),
        })
    }
}
