use crate::data::DataStreamController;
use crate::{
    account::AccountManager,
    matching::{MatchingEngine, OrderBook},
    types::{BacktestRecorder, CancelOrderRequest, CancelOrderRequestWithResponse, Order},
};
use std::collections::HashSet;
use std::sync::Arc;
use tokio::sync::{broadcast, mpsc, Mutex, RwLock};

/// 完成检测器
/// 用于检测所有数据流是否已完成
pub struct CompletionDetector {
    pub completed_streams: HashSet<String>,
    pub expected_streams: HashSet<String>,
}

impl CompletionDetector {
    pub fn new() -> Self {
        let mut expected_streams = HashSet::new();
        expected_streams.insert("quotes".to_string());
        expected_streams.insert("trades".to_string());
        expected_streams.insert("depth".to_string());

        Self {
            completed_streams: HashSet::new(),
            expected_streams,
        }
    }

    pub fn mark_completed(&mut self, stream_name: &str) {
        self.completed_streams.insert(stream_name.to_string());
        tracing::info!("Stream '{}' marked as completed", stream_name);
    }

    pub fn is_completed(&self) -> bool {
        self.completed_streams.len() >= self.expected_streams.len()
    }
}

/// 全局应用状态
pub struct AppState {
    /// 数据流控制器
    pub data_stream_controller: Option<Arc<Mutex<DataStreamController>>>,
    /// 账户管理器
    pub account_manager: Option<Arc<Mutex<AccountManager>>>,
    pub orderbook: Option<Arc<Mutex<OrderBook>>>,
    /// 匹配引擎
    pub matching_engine: Option<Arc<Mutex<MatchingEngine>>>,
    /// 全局update_id计数器
    pub update_id_counter: u64,
    /// 回测记录器
    pub backtest_recorder: Option<Arc<Mutex<BacktestRecorder>>>,
    /// 完成检测器
    pub completion_detector: Option<Arc<Mutex<CompletionDetector>>>,
    /// 取消订单通道发送端（WebSocket使用）
    pub cancel_order_sender: Option<mpsc::Sender<CancelOrderRequest>>,
    /// HTTP取消订单通道发送端（带响应通道）
    pub http_cancel_order_sender: Option<mpsc::Sender<CancelOrderRequestWithResponse>>,
    /// 订单更新广播发送端
    pub order_update_sender: Option<broadcast::Sender<Order>>,
}

impl AppState {
    /// 创建新的应用状态
    pub fn new() -> Self {
        Self {
            data_stream_controller: None,
            account_manager: None,
            orderbook: None,
            matching_engine: None,
            update_id_counter: 0,
            backtest_recorder: None,
            completion_detector: None,
            cancel_order_sender: None,
            http_cancel_order_sender: None,
            order_update_sender: None,
        }
    }

    /// 设置数据流控制器
    pub fn set_data_stream_controller(&mut self, controller: Arc<Mutex<DataStreamController>>) {
        self.data_stream_controller = Some(controller);
    }

    /// 获取数据流控制器
    pub fn data_stream_controller(&self) -> Option<Arc<Mutex<DataStreamController>>> {
        self.data_stream_controller.clone()
    }

    /// 设置账户管理器
    pub fn set_account_manager(&mut self, manager: Arc<Mutex<AccountManager>>) {
        self.account_manager = Some(manager);
    }

    /// 获取账户管理器
    pub fn account_manager(&self) -> Option<Arc<Mutex<AccountManager>>> {
        self.account_manager.clone()
    }

    pub fn set_orderbook(&mut self, orderbook: Arc<Mutex<OrderBook>>) {
        self.orderbook = Some(orderbook);
    }

    pub fn orderbook(&self) -> Option<Arc<Mutex<OrderBook>>> {
        self.orderbook.clone()
    }

    /// 设置匹配引擎
    pub fn set_matching_engine(&mut self, engine: Arc<Mutex<MatchingEngine>>) {
        self.matching_engine = Some(engine);
    }

    /// 获取匹配引擎
    pub fn matching_engine(&self) -> Option<Arc<Mutex<MatchingEngine>>> {
        self.matching_engine.clone()
    }

    /// 设置回测记录器
    pub fn set_backtest_recorder(&mut self, recorder: Arc<Mutex<BacktestRecorder>>) {
        self.backtest_recorder = Some(recorder);
    }

    /// 获取回测记录器
    pub fn backtest_recorder(&self) -> Option<Arc<Mutex<BacktestRecorder>>> {
        self.backtest_recorder.clone()
    }

    /// 设置完成检测器
    pub fn set_completion_detector(&mut self, detector: Arc<Mutex<CompletionDetector>>) {
        self.completion_detector = Some(detector);
    }

    /// 获取完成检测器
    pub fn completion_detector(&self) -> Option<Arc<Mutex<CompletionDetector>>> {
        self.completion_detector.clone()
    }

    /// 设置取消订单通道发送端
    pub fn set_cancel_order_sender(&mut self, sender: mpsc::Sender<CancelOrderRequest>) {
        self.cancel_order_sender = Some(sender);
    }

    /// 获取取消订单通道发送端
    pub fn cancel_order_sender(&self) -> Option<mpsc::Sender<CancelOrderRequest>> {
        self.cancel_order_sender.clone()
    }

    /// 设置HTTP取消订单通道发送端
    pub fn set_http_cancel_order_sender(
        &mut self,
        sender: mpsc::Sender<CancelOrderRequestWithResponse>,
    ) {
        self.http_cancel_order_sender = Some(sender);
    }

    /// 获取HTTP取消订单通道发送端
    pub fn http_cancel_order_sender(&self) -> Option<mpsc::Sender<CancelOrderRequestWithResponse>> {
        self.http_cancel_order_sender.clone()
    }

    /// 设置订单更新广播发送端
    pub fn set_order_update_sender(&mut self, sender: broadcast::Sender<Order>) {
        self.order_update_sender = Some(sender);
    }

    /// 获取订单更新广播发送端
    pub fn order_update_sender(&self) -> Option<broadcast::Sender<Order>> {
        self.order_update_sender.clone()
    }
}

/// 全局应用状态实例
static APP_STATE: RwLock<AppState> = RwLock::const_new(AppState {
    data_stream_controller: None,
    account_manager: None,
    orderbook: None,
    matching_engine: None,
    update_id_counter: 0,
    backtest_recorder: None,
    completion_detector: None,
    cancel_order_sender: None,
    http_cancel_order_sender: None,
    order_update_sender: None,
});

/// 获取全局应用状态
pub async fn get_app_state() -> tokio::sync::RwLockReadGuard<'static, AppState> {
    APP_STATE.read().await
}

/// 获取可变的全局应用状态
pub async fn get_app_state_mut() -> tokio::sync::RwLockWriteGuard<'static, AppState> {
    APP_STATE.write().await
}

/// 设置数据流控制器到全局状态
pub async fn set_data_stream_controller(controller: Arc<Mutex<DataStreamController>>) {
    let mut state = APP_STATE.write().await;
    state.set_data_stream_controller(controller);
}

/// 获取数据流控制器
pub async fn get_data_stream_controller() -> Option<Arc<Mutex<DataStreamController>>> {
    let state = APP_STATE.read().await;
    state.data_stream_controller()
}

/// 设置账户管理器到全局状态
pub async fn set_account_manager(manager: Arc<Mutex<AccountManager>>) {
    let mut state = APP_STATE.write().await;
    state.set_account_manager(manager);
}

/// 获取账户管理器
pub async fn get_account_manager() -> Option<Arc<Mutex<AccountManager>>> {
    let state = APP_STATE.read().await;
    state.account_manager()
}

pub async fn set_orderbook(orderbook: Arc<Mutex<OrderBook>>) {
    let mut state = APP_STATE.write().await;
    state.set_orderbook(orderbook);
}

pub async fn get_orderbook() -> Option<Arc<Mutex<OrderBook>>> {
    let state = APP_STATE.read().await;
    state.orderbook()
}

/// 设置匹配引擎到全局状态
pub async fn set_matching_engine(engine: Arc<Mutex<MatchingEngine>>) {
    let mut state = APP_STATE.write().await;
    state.set_matching_engine(engine);
}

/// 获取匹配引擎
pub async fn get_matching_engine() -> Option<Arc<Mutex<MatchingEngine>>> {
    let state = APP_STATE.read().await;
    state.matching_engine()
}

/// 获取下一个update_id并递增计数器
pub async fn get_next_update_id() -> u64 {
    let mut state = APP_STATE.write().await;
    state.update_id_counter += 1;
    state.update_id_counter
}

/// 获取当前update_id（不递增）
pub async fn get_current_update_id() -> u64 {
    let state = APP_STATE.read().await;
    state.update_id_counter
}

/// 重置update_id计数器
pub async fn reset_update_id_counter() {
    let mut state = APP_STATE.write().await;
    state.update_id_counter = 0;
}

/// 设置回测记录器到全局状态
pub async fn set_backtest_recorder(recorder: Arc<Mutex<BacktestRecorder>>) {
    let mut state = APP_STATE.write().await;
    state.set_backtest_recorder(recorder);
}

/// 获取回测记录器
pub async fn get_backtest_recorder() -> Option<Arc<Mutex<BacktestRecorder>>> {
    let state = APP_STATE.read().await;
    state.backtest_recorder()
}

/// 设置完成检测器到全局状态
pub async fn set_completion_detector(detector: Arc<Mutex<CompletionDetector>>) {
    let mut state = APP_STATE.write().await;
    state.set_completion_detector(detector);
}

/// 获取完成检测器
pub async fn get_completion_detector() -> Option<Arc<Mutex<CompletionDetector>>> {
    let state = APP_STATE.read().await;
    state.completion_detector()
}

/// 设置取消订单通道发送端到全局状态
pub async fn set_cancel_order_sender(sender: mpsc::Sender<CancelOrderRequest>) {
    let mut state = APP_STATE.write().await;
    state.set_cancel_order_sender(sender);
}

/// 获取取消订单通道发送端
pub async fn get_cancel_order_sender() -> Option<mpsc::Sender<CancelOrderRequest>> {
    let state = APP_STATE.read().await;
    state.cancel_order_sender()
}

/// 设置HTTP取消订单通道发送端到全局状态
pub async fn set_http_cancel_order_sender(sender: mpsc::Sender<CancelOrderRequestWithResponse>) {
    let mut state = APP_STATE.write().await;
    state.set_http_cancel_order_sender(sender);
}

/// 获取HTTP取消订单通道发送端
pub async fn get_http_cancel_order_sender() -> Option<mpsc::Sender<CancelOrderRequestWithResponse>>
{
    let state = APP_STATE.read().await;
    state.http_cancel_order_sender()
}

/// 设置订单更新广播发送端到全局状态
pub async fn set_order_update_sender(sender: broadcast::Sender<Order>) {
    let mut state = APP_STATE.write().await;
    state.set_order_update_sender(sender);
}

/// 获取订单更新广播发送端
pub async fn get_order_update_sender() -> Option<broadcast::Sender<Order>> {
    let state = APP_STATE.read().await;
    state.order_update_sender()
}
